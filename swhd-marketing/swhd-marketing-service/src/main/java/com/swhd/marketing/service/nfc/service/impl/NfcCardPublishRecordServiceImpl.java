package com.swhd.marketing.service.nfc.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swhd.marketing.api.nfc.dto.param.record.NfcCardPublishRecordPageParam;
import com.swhd.marketing.service.nfc.entity.NfcCardPublishRecord;
import com.swhd.marketing.service.nfc.mapper.NfcCardPublishRecordMapper;
import com.swhd.marketing.service.nfc.service.NfcCardPublishRecordService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * NFC卡任务发布记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Service
@AllArgsConstructor
public class NfcCardPublishRecordServiceImpl extends BaseHdServiceImpl<NfcCardPublishRecordMapper, NfcCardPublishRecord> implements NfcCardPublishRecordService {

    @Override
    public IPage<NfcCardPublishRecord> page(NfcCardPublishRecordPageParam param) {
        return lambdaQuery()
                .eq(Func.isNotEmpty(param.getTaskId()), NfcCardPublishRecord::getTaskId, param.getTaskId())
                .eq(Func.isNotEmpty(param.getFid()), NfcCardPublishRecord::getFid, param.getFid())
                .eq(Func.isNotEmpty(param.getDraftId()), NfcCardPublishRecord::getDraftId, param.getDraftId())
                .in(Func.isNotEmpty(param.getPublishStatuses()), NfcCardPublishRecord::getPublishStatus, param.getPublishStatuses())
                .in(Func.isNotEmpty(param.getPayStatuses()), NfcCardPublishRecord::getPayStatus, param.getPayStatuses())
                .betweenDateTimeList(param.getPublishTimeBetween(), NfcCardPublishRecord::getPublishTime)
                .betweenDateTimeList(param.getRollbackTimeBetween(), NfcCardPublishRecord::getRollbackTime)
                .orderByDesc(NfcCardPublishRecord::getCreateTime)
                .orderByDesc(NfcCardPublishRecord::getId)
                .page(convertToPage(param));
    }

}
