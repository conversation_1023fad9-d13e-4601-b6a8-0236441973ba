package com.swhd.agent.web.tenant.fund.web;

import com.fasterxml.jackson.databind.JsonNode;
import com.swhd.agent.api.fund.client.FundOceanengineCustomRechargeClient;
import com.swhd.agent.api.fund.client.FundOceanengineTransferRecordClient;
import com.swhd.agent.api.fund.dto.param.recharge.FundOceanengineCustomRechargeAddParam;
import com.swhd.agent.api.fund.dto.param.recharge.FundOceanengineCustomRechargeAddTempParam;
import com.swhd.agent.api.fund.dto.param.recharge.FundOceanengineCustomRechargePageParam;
import com.swhd.agent.api.fund.dto.param.recharge.FundOceanengineCustomRechargeUpdateParam;
import com.swhd.agent.api.fund.dto.result.FundOceanengineCustomRechargeResult;
import com.swhd.agent.api.fund.dto.result.FundOceanengineTransferRecordResult;
import com.swhd.agent.web.tenant.common.constant.WebConstant;
import com.swhd.agent.web.tenant.fund.mq.consumer.AgentFundContentDownloadConsumer;
import com.swhd.agent.web.tenant.fund.service.FundOceanengineTransferRecordService;
import com.swhd.agent.web.tenant.fund.vo.result.FundOceanengineCustomRechargeResultVo;
import com.swhd.agent.web.tenant.fund.vo.result.FundOceanengineTransferRecordResultVo;
import com.swhd.content.api.download.client.DownloadExportRecordClient;
import com.swhd.content.api.download.dto.param.export.DownloadExportRecordAddParam;
import com.swhd.crm.api.custom.wrapper.CustomInfoWrapper;
import com.swhd.magiccube.core.auth.CurrentUserHolder;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.JsonUtil;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/8
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/fundOceanengineCustomRecharge")
public class FundOceanengineCustomRechargeController {

    private final FundOceanengineCustomRechargeClient fundOceanengineCustomRechargeClient;

    private final FundOceanengineTransferRecordClient fundOceanengineTransferRecordClient;

    private final FundOceanengineTransferRecordService fundOceanengineTransferRecordService;

    private final DownloadExportRecordClient downloadExportRecordClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<FundOceanengineCustomRechargeResultVo>> page(@RequestBody @Valid FundOceanengineCustomRechargePageParam param) {
        Rsp<PageResult<FundOceanengineCustomRechargeResult>> pageRsp = fundOceanengineCustomRechargeClient.page(param);
        if (RspHd.isFail(pageRsp)) {
            return RspHd.fail(pageRsp);
        }
        PageResult<FundOceanengineCustomRechargeResultVo> voPage = PageUtil.convert(pageRsp.getData(),
                FundOceanengineCustomRechargeResultVo.class);
        // 设置客户信息
        CustomInfoWrapper.getInstance().setList(voPage.getRecords(), FundOceanengineCustomRechargeResultVo::getCustomId,
                FundOceanengineCustomRechargeResultVo::setCustomInfo);
        // 设置客户渠道信息
        CustomInfoWrapper.getInstance().setList(voPage.getRecords(), FundOceanengineCustomRechargeResultVo::getCustomChannelId,
                FundOceanengineCustomRechargeResultVo::setCustomChannelInfo);
        return RspHd.data(voPage);
    }

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    public Rsp<FundOceanengineCustomRechargeResult> getById(@RequestParam("id") Long id) {
        return fundOceanengineCustomRechargeClient.getById(id);
    }

    @Operation(summary = "详情")
    @GetMapping("/detail")
    public Rsp<FundOceanengineCustomRechargeResultVo> detail(@RequestParam("id") Long id) {
        Rsp<FundOceanengineCustomRechargeResult> rsp = fundOceanengineCustomRechargeClient.getById(id);
        if (RspHd.failOrDataIsNull(rsp)) {
            return RspHd.fail(rsp);
        }
        FundOceanengineCustomRechargeResultVo vo = Func.copy(rsp.getData(), FundOceanengineCustomRechargeResultVo.class);
        // 设置客户信息
        CustomInfoWrapper.getInstance().setInfo(vo, FundOceanengineCustomRechargeResultVo::getCustomId,
                FundOceanengineCustomRechargeResultVo::setCustomInfo);
        // 设置客户渠道信息
        CustomInfoWrapper.getInstance().setInfo(vo, FundOceanengineCustomRechargeResultVo::getCustomChannelId,
                FundOceanengineCustomRechargeResultVo::setCustomChannelInfo);
        // 转账记录
        Rsp<List<FundOceanengineTransferRecordResult>> transferRecordListRsp = fundOceanengineTransferRecordClient.listByRechargeId(vo.getId());
        if (RspHd.isFail(transferRecordListRsp)) {
            return RspHd.fail(rsp);
        }
        List<FundOceanengineTransferRecordResultVo> transferRecordVoList = Func.copy(transferRecordListRsp.getData(),
                FundOceanengineTransferRecordResultVo.class);
        fundOceanengineTransferRecordService.fillVoInfo(transferRecordVoList);
        vo.setTransferRecordList(transferRecordVoList);
        return RspHd.data(vo);
    }

    @Operation(summary = "新增")
    @PostMapping("/add")
    public Rsp<Void> add(@RequestBody @Valid FundOceanengineCustomRechargeAddParam param) {
        return fundOceanengineCustomRechargeClient.add(param);
    }

    @Operation(summary = "新增(temp)", description = "新增未充值状态的数据")
    @PostMapping("/addTemp")
    public Rsp<Void> addTemp(@RequestBody @Valid FundOceanengineCustomRechargeAddTempParam param) {
        return fundOceanengineCustomRechargeClient.addTemp(param);
    }

    @Operation(summary = "修改")
    @PostMapping("/update")
    public Rsp<Void> update(@RequestBody @Valid FundOceanengineCustomRechargeUpdateParam param) {
        return fundOceanengineCustomRechargeClient.update(param);
    }

    @Operation(summary = "删除")
    @PostMapping("/removeById")
    public Rsp<Void> removeById(@RequestParam("id") Long id) {
        return fundOceanengineCustomRechargeClient.removeById(id);
    }

    @Operation(summary = "下载")
    @PostMapping("/download")
    public Rsp<Void> download(@RequestBody @Valid FundOceanengineCustomRechargePageParam param) {
        DownloadExportRecordAddParam exportRecordAddParam = new DownloadExportRecordAddParam();
        exportRecordAddParam.setUserId(CurrentUserHolder.currentUserId());
        exportRecordAddParam.setExportType(AgentFundContentDownloadConsumer.OCEANENGINE_CUSTOM_RECHARGE_EXPORT_TYPE);
        exportRecordAddParam.setExportParams(JsonUtil.convertValue(param, JsonNode.class));
        return downloadExportRecordClient.add(exportRecordAddParam);
    }

}
