package com.swhd.agent.web.tenant.account.vo.poi;

import cn.hutool.core.collection.CollectionUtil;
import com.swhd.agent.api.account.dto.result.AccountOceanengineOperatorStatisticsResult;
import com.swhd.agent.web.tenant.account.vo.result.AccountOceanengineOptLogStatisticsResultVo;
import com.swhd.crm.api.custom.dto.result.CustomInfoResult;
import com.swhd.magiccube.easypoi.interfaces.IPoiIndex;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/28
 */
@Getter
@Setter
@Schema(description = "AccountOceanengineOptLogStatisticsResultPoiExcelVo对象")
public class AccountOceanengineOptLogStatisticsResultPoiExcelVo extends AccountOceanengineOptLogStatisticsResultVo implements IPoiIndex {

    private Integer index;

    public String getCustomChannelName(){
        CustomInfoResult customInfo = getCustomChannelInfo();
        if (customInfo == null) {
            return null;
        }
        if (customInfo.getCustomType() == null) {
            return customInfo.getName();
        }
        return String.format("%s(%s)", customInfo.getName(), customInfo.getCustomType().getTitle());
    }

    public String getCustomName() {
        CustomInfoResult customInfo = getCustomInfo();
        if (customInfo == null) {
            return null;
        }
        if (customInfo.getCustomType() == null) {
            return customInfo.getName();
        }
        return String.format("%s(%s)", customInfo.getName(), customInfo.getCustomType().getTitle());
    }

    public String getOperator(){
        List<AccountOceanengineOperatorStatisticsResult> operatorStatistics = getOperatorStatistics();
        if (CollectionUtil.isEmpty(operatorStatistics)){
            return null;
        }
        //拼接字符串
        return operatorStatistics.stream()
                .map(item -> String.format("%s(%d次)", item.getOperator(), item.getOperationCount()))
                .collect(Collectors.joining("\r\n"));
    }

    public String getUsingVpnText(){
        if (getUsingVpn() == null){
            return null;
        }
        return getUsingVpn() ? "是" : "否";
    }

    public String getHasConsumeText(){
        if (getHasConsume() == null){
            return null;
        }
        return getHasConsume() ? "是" : "否";
    }

}
