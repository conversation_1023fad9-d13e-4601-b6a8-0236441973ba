package com.swhd.shop.api.connect.enums;

import com.swhd.magiccube.core.exception.ServiceException;
import com.swj.magiccube.tool.enumeration.IntEnumCodeDesc;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/22 16:28
 */

@Getter
@AllArgsConstructor
public enum FolderType implements IntEnumCodeDesc<FolderType> {

    VIDEO(0, "视频"),
    
    STORE(1, "门店"),
    
    GOODS(2, "产品"),
    
    ACTIVITY(3, "活动"),
    
    OTHER(4, "其他");

    private final Integer code;

    private final String desc;

    private static final Map<Integer,FolderType> CACHE;

    static {
        final Map<Integer,FolderType> map = new HashMap<>();
        for (FolderType value : FolderType.values()){
            map.put(value.getCode(),value);
        }
        CACHE = Collections.unmodifiableMap(map);
    }

    public static FolderType of(Integer code){
        return CACHE.get(code);
    }

    public static FolderType ofRequired(Integer code){
        final FolderType type = of(code);
        if(Objects.nonNull(type)){
            return type;
        }
        throw new ServiceException("不支持的类型");
    }

}


