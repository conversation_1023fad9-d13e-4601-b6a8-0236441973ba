package com.swhd.shop.api.merchandise.dto.param.info;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-12-26
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "SpuInfoAddParam对象")
public class SpuInfoAddParam {

    @NotEmpty
    @Schema(description = "商品名称")
    private String spuName;

    @Schema(description = "商品描述")
    private String spuDescription;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "卖点")
    private String sellingPoint;

    @NotNull
    @Schema(description = "商品原始价格")
    private BigDecimal originalPrice;

    @NotEmpty
    @Schema(description = "商品主图URL")
    private List<String> mainImages;

    @NotNull
    @Schema(description = "商品详情图片URLs，多个图片用逗号分隔")
    private SpuDetailImages detailImages;

    @Schema(description = "商品URL")
    private String accessingUrl;

    @NotEmpty
    @Schema(description = "门店ID列表")
    private List<Long> shopIds;

    @NotEmpty
    @Schema(description = "SKU 列表")
    private List<SpuInfoSkuSaveParam> skuList;

}
