package com.swhd.shop.service.common.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 文件映射表实体类
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("tcloud_shop_file_mapping")
public class FileMapping extends BaseHdEntity {

    @Schema(description = "原始Key")
    private String originKey;

    @Schema(description = "目标URL")
    private String targetUrl;

    @Schema(description = "状态：0-待处理，1-处理完成，2-处理失败")
    private Integer status;

    @Schema(description = "目标类型")
    private String targetType;

}
