package com.swhd.shop.service.amap.entity;

import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;

/**
 * 云店高德商户通订单表实体类
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Getter
@Setter
@Accessors(chain = true)
@FieldNameConstants
@TableName("tcloud_shop_amap_order")
public class AmapOrder extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "门店ID")
    private Long shopId;

    @Schema(description = "skuId")
    private String skuId;

    @Schema(description = "订单ID")
    private String orderId;

    @Schema(description = "售价")
    private BigDecimal payAmount;

    @Schema(description = "失败信息")
    private String errorMsg;

    @Schema(description = "下单时间")
    private LocalDateTime orderTime;

    @Schema(description = "购买场景  \"new\": \"新购\",\"renew\": \"续费\",\"upgrade\": \"升级\",\"extend\",：\"继承\"")
    private String purchaseScene;

    @Schema(description = "订单状态；1 - 生效中... 0 - 未生效... 2 - 已失效")
    private String orderState;

    @Schema(description = "生效时间")
    private LocalDateTime effectiveDate;

    @Schema(description = "失效时间")
    private LocalDateTime expireDate;

    @Schema(description = "审核状态 NO_REVIEW：无需审核,AGREE： 审核通过，DISAGREE： 审核拒绝;")
    private String auditState;

    @Schema(description = "审核描述")
    private String auditDescribe;

    @Schema(description = "平台门店ID")
    private String platformShopId;

}
