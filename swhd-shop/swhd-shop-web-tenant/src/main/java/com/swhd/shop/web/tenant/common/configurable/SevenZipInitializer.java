package com.swhd.shop.web.tenant.common.configurable;

import com.swj.magiccube.aot.annotation.AotHints;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import net.sf.sevenzipjbinding.ArchiveFormat;
import net.sf.sevenzipjbinding.SevenZip;
import org.springframework.stereotype.Component;

/**
 * SevenZip初始化器
 *
 * <AUTHOR> <EMAIL>
 * @since 2025/2/8
 */
@Slf4j
@Component
@AotHints(
        autoReflectionPackages = {"net.sf.sevenzipjbinding"},
        jniClass = {SevenZip.class},
        reflectionClass = {ArchiveFormat.class},
        resources = {"Linux-amd64/*"}
)
public class SevenZipInitializer {

    @PostConstruct
    public void init() {
        try {
            SevenZip.initSevenZipFromPlatformJAR();
            log.info("SevenZip initialized successfully.");
        } catch (Exception e) {
            log.error("Failed to initialize SevenZ<PERSON>", e);
        }
    }

}
