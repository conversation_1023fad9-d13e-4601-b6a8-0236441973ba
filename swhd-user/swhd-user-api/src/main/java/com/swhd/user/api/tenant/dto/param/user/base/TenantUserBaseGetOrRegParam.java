package com.swhd.user.api.tenant.dto.param.user.base;

import com.swhd.magiccube.core.annotation.JsonMaskType;
import com.swhd.magiccube.core.annotation.JsonOss;
import com.swhd.magiccube.core.annotation.LogMask;
import com.swhd.magiccube.core.constant.PatternConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-03-03
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "TenantUserBaseGetOrRegParam对象")
public class TenantUserBaseGetOrRegParam {

    @LogMask(type = JsonMaskType.MOBILE)
    @Schema(description = "手机号")
    @NotEmpty(message = "手机号不能为空")
    @Pattern(regexp = PatternConstant.SIMPLE_MOBILE, message = "手机号码格式不正确")
    private String mobile;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

}
