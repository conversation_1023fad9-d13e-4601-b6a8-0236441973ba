package com.swhd.user.api.tenant.client;

import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swhd.user.api.common.constant.ApiConstant;
import com.swhd.user.api.tenant.dto.param.info.TenantAgentInfoAddParam;
import com.swhd.user.api.tenant.dto.param.info.TenantAgentInfoPageParam;
import com.swhd.user.api.tenant.dto.param.info.TenantAgentInfoUpdateParam;
import com.swhd.user.api.tenant.dto.result.TenantAgentInfoResult;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-17
 */
@Validated
@FeignClient(value = ApiConstant.APP_NAME, url = ApiConstant.FEIGN_URL, path = TenantAgentInfoClient.BASE_PATH)
public interface TenantAgentInfoClient {

    String BASE_PATH = ApiConstant.BASE_PATH + "/tenant/agent/info";

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    Rsp<PageResult<TenantAgentInfoResult>> page(@RequestBody @Valid TenantAgentInfoPageParam param);

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    Rsp<TenantAgentInfoResult> getById(@RequestParam("id") Long id);

    @Operation(summary = "根据id列表获取")
    @PostMapping("/listByIds")
    Rsp<List<TenantAgentInfoResult>> listByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

    @Operation(summary = "新增")
    @PostMapping("/add")
    Rsp<Void> add(@RequestBody @Valid TenantAgentInfoAddParam param);

    @Operation(summary = "修改")
    @PostMapping("/update")
    Rsp<Void> update(@RequestBody @Valid TenantAgentInfoUpdateParam param);

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids);

}
