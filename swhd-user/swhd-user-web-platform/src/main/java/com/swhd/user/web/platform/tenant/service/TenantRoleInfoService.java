package com.swhd.user.web.platform.tenant.service;

import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.user.api.tenant.client.TenantRoleInfoClient;
import com.swhd.user.api.tenant.client.TenantUserRoleClient;
import com.swhd.user.api.tenant.dto.result.TenantRoleInfoResult;
import com.swhd.user.api.tenant.dto.result.TenantUserRoleResult;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Service
@AllArgsConstructor
public class TenantRoleInfoService {

    private final TenantRoleInfoClient tenantRoleInfoClient;

    private final TenantUserRoleClient tenantUserRoleClient;

    /**
     * 根据userTenantId获取角色
     *
     * @param userTenantId 用户租户ID
     * @return List
     */
    public List<TenantRoleInfoResult> listByUserTenantId(Long userTenantId) {
        if (userTenantId == null) {
            return Collections.emptyList();
        }
        Rsp<List<TenantUserRoleResult>> userRoleRsp = tenantUserRoleClient.listByUserTenantId(userTenantId);
        RspHd.failThrowException(userRoleRsp);
        if (Func.isEmpty(userRoleRsp.getData())) {
            return Collections.emptyList();
        }
        List<Long> roleIds = userRoleRsp.getData().stream().map(TenantUserRoleResult::getRoleId).distinct().toList();
        Rsp<List<TenantRoleInfoResult>> roleListRsp = tenantRoleInfoClient.listByIds(roleIds);
        RspHd.failThrowException(roleListRsp);
        return roleListRsp.getData();

    }

    /**
     * 根据userTenantId列表获取角色
     *
     * @param userTenantIds 用户租户ID列表
     * @return Map<userTenantId, List < RoleResult>>
     */
    public Map<Long, List<TenantRoleInfoResult>> mapByUserTenantIds(List<Long> userTenantIds) {
        if (Func.isEmpty(userTenantIds)) {
            return Collections.emptyMap();
        }
        Rsp<List<TenantUserRoleResult>> userRoleRsp = tenantUserRoleClient.listByUserTenantIds(userTenantIds);
        RspHd.failThrowException(userRoleRsp);
        if (Func.isEmpty(userRoleRsp.getData())) {
            return Collections.emptyMap();
        }

        List<Long> roleIds = userRoleRsp.getData().stream().map(TenantUserRoleResult::getRoleId).distinct().toList();
        Rsp<List<TenantRoleInfoResult>> roleListRsp = tenantRoleInfoClient.listByIds(roleIds);
        RspHd.failThrowException(roleListRsp);
        if (Func.isEmpty(roleListRsp.getData())) {
            return Collections.emptyMap();
        }
        // Map<roleId, TenantRoleInfoResult>
        Map<Long, TenantRoleInfoResult> roleMap = roleListRsp.getData().stream().collect(Collectors.toMap(TenantRoleInfoResult::getId, r -> r));

        // userRoleList分组，同时把TenantUserRoleResult转为TenantRoleInfoResult
        return userRoleRsp.getData().stream()
                .filter(ur -> roleMap.containsKey(ur.getRoleId()))
                .collect(Collectors.groupingBy(TenantUserRoleResult::getUserTenantId, Collectors.mapping(ur -> roleMap.get(ur.getRoleId()), Collectors.toList())));
    }

}
