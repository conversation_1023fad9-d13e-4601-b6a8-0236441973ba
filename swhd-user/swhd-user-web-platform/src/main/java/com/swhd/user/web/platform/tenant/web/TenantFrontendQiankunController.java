package com.swhd.user.web.platform.tenant.web;

import com.swhd.user.api.tenant.client.TenantFrontendQiankunClient;
import com.swhd.user.api.tenant.dto.param.qiankun.TenantFrontendQiankunAddParam;
import com.swhd.user.api.tenant.dto.param.qiankun.TenantFrontendQiankunPageParam;
import com.swhd.user.api.tenant.dto.param.qiankun.TenantFrontendQiankunUpdateParam;
import com.swhd.user.api.tenant.dto.result.TenantFrontendQiankunResult;
import com.swhd.user.web.platform.common.constant.WebConstant;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2025/1/21
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/tenantFrontendQiankun")
public class TenantFrontendQiankunController {

    private final TenantFrontendQiankunClient tenantFrontendQiankunClient;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<TenantFrontendQiankunResult>> page(@RequestBody @Valid TenantFrontendQiankunPageParam param) {
        return tenantFrontendQiankunClient.page(param);
    }

    @Operation(summary = "根据id获取")
    @GetMapping("/getById")
    public Rsp<TenantFrontendQiankunResult> getById(@RequestParam("id") Long id) {
        return tenantFrontendQiankunClient.getById(id);
    }

    @Operation(summary = "新增")
    @PostMapping("/add")
    public Rsp<Void> add(@RequestBody @Valid TenantFrontendQiankunAddParam param) {
        return tenantFrontendQiankunClient.add(param);
    }

    @Operation(summary = "修改")
    @PostMapping("/update")
    public Rsp<Void> update(@RequestBody @Valid TenantFrontendQiankunUpdateParam param) {
        return tenantFrontendQiankunClient.update(param);
    }

    @Operation(summary = "批量删除")
    @PostMapping("/removeByIds")
    public Rsp<Void> removeByIds(@RequestBody @Valid @NotEmpty Collection<Long> ids) {
        return tenantFrontendQiankunClient.removeByIds(ids);
    }

}
