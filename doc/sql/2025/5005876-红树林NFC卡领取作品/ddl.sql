CREATE TABLE `tmarketing_nfc_card_info`
(
    `id`          bigint       NOT NULL COMMENT '主键id',
    `tenant_id`   bigint       NOT NULL DEFAULT '0' COMMENT '租户id',
    `name`        varchar(32)  NOT NULL DEFAULT '' COMMENT '名称',
    `fid`         varchar(50)  NOT NULL DEFAULT '' COMMENT 'NFC卡FID',
    `quota`       int          NOT NULL DEFAULT '0' COMMENT 'NFC卡额度',
    `consumption` int          NOT NULL DEFAULT '0' COMMENT '已消耗',
    `state`       tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `remark`      varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `create_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id`  varchar(32)  NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32)  NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete`   tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY           `idx_tenantid_createtime` (`tenant_id`,`create_time`),
    KEY           `idx_fid` (`fid`)
) ENGINE=InnoDB COMMENT='NFC卡信息表';


CREATE TABLE `tmarketing_nfc_card_mcn_task`
(
    `id`          bigint      NOT NULL COMMENT '主键id',
    `tenant_id`   bigint      NOT NULL DEFAULT '0' COMMENT '租户id',
    `task_id`     bigint      NOT NULL DEFAULT '0' COMMENT '任务ID',
    `card_id`     bigint      NOT NULL DEFAULT '0' COMMENT 'NFC卡ID',
    `create_time` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id`  varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id` varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete`   tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY           `uk_card_id` (`card_id`),
    KEY           `idx_tenantid_createtime` (`tenant_id`,`create_time`),
    KEY           `idx_task_id` (`task_id`)
) ENGINE=InnoDB COMMENT='任务NFC卡绑定关系表';

CREATE TABLE `tmarketing_nfc_card_publish_record`
(
    `id`             bigint      NOT NULL COMMENT '主键id',
    `tenant_id`      bigint      NOT NULL DEFAULT '0' COMMENT '租户id',
    `task_id`        bigint      NOT NULL DEFAULT '0' COMMENT '任务ID',
    `fid`            varchar(50) NOT NULL DEFAULT '' COMMENT 'NFC卡FID',
    `draft_id`       bigint      NOT NULL DEFAULT '0' COMMENT '草稿ID',
    `publish_status` tinyint     NOT NULL DEFAULT '0' COMMENT '发布状态：1-发布中，2-发布成功，3-发布失败',
    `pay_status`     tinyint     NOT NULL DEFAULT '0' COMMENT '支付状态：1-已支付，2-已回滚',
    `publish_time`   datetime             DEFAULT NULL COMMENT '发布时间',
    `rollback_time`  datetime             DEFAULT NULL COMMENT '回滚时间',
    `create_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`    datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator_id`     varchar(32) NOT NULL DEFAULT '' COMMENT '创建人id',
    `modifier_id`    varchar(32) NOT NULL DEFAULT '' COMMENT '修改人id',
    `is_delete`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY              `idx_task_id` (`task_id`),
    KEY              `idx_draft_id` (`draft_id`),
    KEY              `idx_fid` (`fid`),
    KEY              `idx_tenantid_createtime` (`tenant_id`,`create_time`),
    KEY              `idx_publish_status` (`publish_status`),
    KEY              `idx_pay_status` (`pay_status`)
) ENGINE=InnoDB  COMMENT='NFC卡任务发布记录表';






