package com.swhd.ai.kefu.api.kefu.dto.param.consume;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-11-20
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "KefuRightsConsumePageParam对象")
public class KefuRightsConsumePageParam extends PageReq {

    @Schema(description = "AI客服用户ID")
    private List<Long> userIds;

    @Schema(description = "消耗时间-开始")
    private LocalDateTime consumeTimeBegin;

    @Schema(description = "消耗时间-结束")
    private LocalDateTime consumeTimeEnd;

    @Schema(description = "消耗状态：0-待扣费 1-已扣费 2-待回滚 3-已回滚")
    private List<Integer> consumeStatuses;

    @Schema(description = "消耗扣费流水号")
    private String consumeTransactionId;

}
