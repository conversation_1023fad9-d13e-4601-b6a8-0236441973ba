package com.swhd.ai.kefu.api.web.dto.param.capital;

import com.swj.magiccube.api.PageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-10-24
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "WebCustomerCapitalPageParam对象")
public class WebCustomerCapitalPageParam extends PageReq {

    @Schema(description = "网页平台id")
    private Long webPlatformId;

    @Schema(description = "用户openId")
    private String userOpenId;

}
