package com.swhd.ai.kefu.api.platform.dto.param.info;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-05-21
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "PlatformInfoAddParam对象")
public class PlatformInfoAddParam {

    @NotNull(message = "主键id不能为空")
    @Schema(description = "机器人平台")
    private Integer robotPlatform;

    @NotEmpty(message = "授权平台的openId不能为空")
    @Schema(description = "授权平台的openId/appId/userId")
    private String oauthOpenId;

    @Schema(description = "客服名称")
    private String kefuName;

}
