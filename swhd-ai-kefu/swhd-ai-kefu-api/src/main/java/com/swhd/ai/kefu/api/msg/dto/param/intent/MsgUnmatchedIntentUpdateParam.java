package com.swhd.ai.kefu.api.msg.dto.param.intent;

import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2025-03-24
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "MsgUnmatchedIntentUpdateParam对象")
public class MsgUnmatchedIntentUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "意图名称")
    private String intentName;

    @Schema(description = "处理状态：0-未处理，1-已添加到知识库，2-已忽略")
    private Integer handleState;

    @Schema(description = "处理时间")
    private LocalDateTime handleTime;

}
