package com.swhd.ai.kefu.service.robot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.ai.kefu.api.robot.dto.param.chasefans.time.RobotChaseFansTimePageParam;
import com.swhd.ai.kefu.api.robot.dto.param.chasefans.time.RobotChaseFansTimeSaveParam;
import com.swhd.ai.kefu.service.robot.entity.RobotChaseFansTime;
import com.swhd.magiccube.mybatis.base.IBaseHdService;
import com.swj.magiccube.api.Rsp;

import java.util.Collection;
import java.util.List;

/**
 * 机器人追粉时间表 服务类
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
public interface RobotChaseFansTimeService extends IBaseHdService<RobotChaseFansTime> {

    /**
     * 分页查询
     *
     * @param param 查询参数
     * @return IPage
     */
    IPage<RobotChaseFansTime> page(RobotChaseFansTimePageParam param);

    /**
     * 根据机器人id获取
     *
     * @param robotId 机器人id
     * @return RobotChaseFansTime
     */
    RobotChaseFansTime getByRobotId(Long robotId);

    /**
     * 根据机器人id列表获取
     *
     * @param robotIds 机器人id列表
     * @return List
     */
    List<RobotChaseFansTime> listByRobotIds(Collection<Long> robotIds);

    /**
     * 复制机器人配置
     *
     * @param templateRobotId 模板机器人id
     * @param toRobotId       复制到的机器人id
     */
    void copy(Long templateRobotId, Long toRobotId);

    /**
     * 保存
     *
     * @param param 参数
     * @return Rsp
     */
    Rsp<Void> save(RobotChaseFansTimeSaveParam param);

}
