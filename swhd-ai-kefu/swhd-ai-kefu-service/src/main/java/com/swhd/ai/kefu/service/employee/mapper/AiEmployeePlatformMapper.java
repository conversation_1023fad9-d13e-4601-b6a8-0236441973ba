package com.swhd.ai.kefu.service.employee.mapper;

import com.swhd.magiccube.mybatis.base.BaseHdMapper;
import com.swhd.ai.kefu.service.employee.entity.AiEmployeePlatform;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI员工渠道关联表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-02-14
 */
public interface AiEmployeePlatformMapper extends BaseHdMapper<AiEmployeePlatform> {

    /**
     * 查询已绑定的平台ID列表
     * @param employeeType 员工类型
     * @return 平台ID列表
     */
    List<Long> listBoundPlatformIds(@Param("employeeType") Integer employeeType);
}
