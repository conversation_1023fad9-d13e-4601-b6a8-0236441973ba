package com.swhd.message.service.behavior.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swhd.message.api.behavior.client.BehaviorImageOriginalClient;
import com.swhd.message.api.behavior.dto.param.image.BehaviorImageOriginalAddParam;
import com.swhd.message.api.behavior.dto.param.image.BehaviorImageOriginalPageParam;
import com.swhd.message.api.behavior.dto.result.BehaviorImageOriginalResult;
import com.swhd.message.service.behavior.entity.BehaviorImageOriginal;
import com.swhd.message.service.behavior.service.BehaviorImageOriginalService;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-20
 */
@RestController
@AllArgsConstructor
@RequestMapping(BehaviorImageOriginalClient.BASE_PATH)
public class BehaviorImageOriginalController implements BehaviorImageOriginalClient {

    private final BehaviorImageOriginalService behaviorImageOriginalService;

    @Override
    public Rsp<PageResult<BehaviorImageOriginalResult>> page(BehaviorImageOriginalPageParam param) {
        IPage<BehaviorImageOriginal> iPage = behaviorImageOriginalService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, BehaviorImageOriginalResult.class));
    }

    @Override
    public Rsp<List<BehaviorImageOriginalResult>> listAll() {
        List<BehaviorImageOriginal> list = behaviorImageOriginalService.list();
        return RspHd.data(Func.copy(list, BehaviorImageOriginalResult.class));
    }

    @Override
    public Rsp<BehaviorImageOriginalResult> getById(Long id) {
        BehaviorImageOriginal entity = behaviorImageOriginalService.getById(id);
        return RspHd.data(Func.copy(entity, BehaviorImageOriginalResult.class));
    }

    @Override
    public Rsp<List<BehaviorImageOriginalResult>> listByIds(Collection<Long> ids) {
        List<BehaviorImageOriginal> list = behaviorImageOriginalService.listByIds(ids);
        return RspHd.data(Func.copy(list, BehaviorImageOriginalResult.class));
    }

    @Override
    public Rsp<Void> add(BehaviorImageOriginalAddParam param) {
        boolean result = behaviorImageOriginalService.save(Func.copy(param, BehaviorImageOriginal.class));
        return RspHd.status(result);
    }

    @Override
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        boolean result = behaviorImageOriginalService.removeByIds(ids);
        return RspHd.status(result);
    }

}
