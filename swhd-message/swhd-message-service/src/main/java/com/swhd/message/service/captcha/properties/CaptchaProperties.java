package com.swhd.message.service.captcha.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <AUTHOR>
 * @since 2024/1/11
 */
@Getter
@Setter
@ConfigurationProperties(CaptchaProperties.PREFIX)
@Component
public class CaptchaProperties {

    public static final String PREFIX = "message.captcha";

    /**
     * 验证码（短码）长度
     */
    private int length = 6;

    /**
     * 验证码存活时间
     */
    private Duration codeTime = Duration.ofMinutes(5);

    /**
     * 验证码删除过度时间
     */
    private Duration codeDeleteTime = Duration.ofSeconds(30);

    /**
     * 验证码验证限制次数
     */
    private int limitNumber = 3;

    /**
     * 验证码token存活时间
     */
    private Duration tokenTime = Duration.ofHours(2);

}
