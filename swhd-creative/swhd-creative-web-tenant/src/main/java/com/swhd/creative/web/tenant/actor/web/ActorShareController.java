package com.swhd.creative.web.tenant.actor.web;

import com.fasterxml.jackson.core.type.TypeReference;
import com.swhd.content.api.type.wrapper.TypeInfoWrapper;
import com.swhd.creative.api.actor.client.ActorInfoClient;
import com.swhd.creative.api.actor.dto.param.actor.info.ActorInfoPageParam;
import com.swhd.creative.api.actor.dto.result.ActorInfoResult;
import com.swhd.creative.web.tenant.actor.properties.ActorProperties;
import com.swhd.creative.web.tenant.actor.service.ActorVideoService;
import com.swhd.creative.web.tenant.actor.vo.result.ActorInfoSharePageResultVo;
import com.swhd.creative.web.tenant.actor.vo.result.ActorInfoShareResultVo;
import com.swhd.creative.web.tenant.constant.WebConstant;
import com.swhd.magiccube.core.auth.Auth;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.redis.utils.RedisUtil;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.ParameterUtil;
import com.swhd.magiccube.web.log.utils.WebSecureUtil;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/2/26
 */
@RestController
@AllArgsConstructor
@RequestMapping(WebConstant.BASE_PATH + "/actorShare")
public class ActorShareController {

    private final ActorInfoClient actorInfoClient;

    private final ActorVideoService actorVideoService;

    private final ActorProperties actorProperties;

    @Operation(summary = "分页查询")
    @PostMapping("/page")
    public Rsp<PageResult<ActorInfoSharePageResultVo>> page(@RequestBody @Valid ActorInfoPageParam param) {
        Rsp<PageResult<ActorInfoResult>> pageRsp = actorInfoClient.page(param);
        if (RspHd.isFail(pageRsp)) {
            return RspHd.fail(pageRsp);
        }
        PageResult<ActorInfoSharePageResultVo> pageVo = PageUtil.convert(pageRsp.getData(), ActorInfoSharePageResultVo.class);
        // 风格类型
        TypeInfoWrapper.getInstance().setListOfIds(pageVo.getRecords(),
                ActorInfoSharePageResultVo::getStyleTypeIds, ActorInfoSharePageResultVo::setStyleTypes);
        // 擅长行业类型
        TypeInfoWrapper.getInstance().setListOfIds(pageVo.getRecords(),
                ActorInfoSharePageResultVo::getProficientIndustryTypeIds, ActorInfoSharePageResultVo::setProficientIndustryTypes);
        // 擅长演出类型
        TypeInfoWrapper.getInstance().setListOfIds(pageVo.getRecords(),
                ActorInfoSharePageResultVo::getProficientShowTypeIds, ActorInfoSharePageResultVo::setProficientShowTypes);
        actorVideoService.setVideos(pageVo.getRecords(),
                ActorInfoSharePageResultVo::getId, ActorInfoSharePageResultVo::setVideos);
        return RspHd.data(pageVo);
    }

    @Operation(summary = "生成h5地址(多个演员)")
    @PostMapping("/genListH5Url")
    public Rsp<String> genListH5Url(@RequestBody @NotEmpty(message = "演员不能为空") List<Long> actorIds) {
        if (actorIds.size() == 1) {
            return genH5Url(actorIds.getFirst());
        }
        if (actorIds.size() > actorProperties.getShareMaxNum()) {
            return RspHd.fail("超过分享限制数：" + actorProperties.getShareMaxNum());
        }
        Rsp<List<ActorInfoResult>> listRsp = actorInfoClient.listByIds(actorIds);
        if (RspHd.isFail(listRsp)) {
            return RspHd.fail(listRsp);
        }
        actorIds = listRsp.getData().stream().map(ActorInfoResult::getId).toList();
        if (Func.isEmpty(actorIds)) {
            return RspHd.fail("无演员信息");
        }
        String key = Func.randomUUID();
        RedisUtil.set(getListCacheKey(key), actorIds, actorProperties.getShareCacheDuration());
        Map<String, String> params = Map.of("key", key);
        return RspHd.data(ParameterUtil.format(actorProperties.getActorListShareH5Url(), params));
    }

    @Operation(summary = "生成h5地址")
    @GetMapping("/genH5Url")
    public Rsp<String> genH5Url(@RequestParam("actorId") Long actorId) {
        Rsp<ActorInfoResult> rsp = actorInfoClient.getById(actorId);
        if (RspHd.isFail(rsp)) {
            return RspHd.fail(rsp);
        }
        if (rsp.getData() == null) {
            return RspHd.fail("无演员信息");
        }
        Map<String, String> params = Map.of("actorId", WebSecureUtil.aesEncryptParam(actorId.toString()));
        return RspHd.data(ParameterUtil.format(actorProperties.getActorShareH5Url(), params));
    }

    @Operation(summary = "演员列表")
    @GetMapping("/actorList")
    @Auth(way = Auth.Way.ANONYMOUS)
    public Rsp<List<ActorInfoShareResultVo>> actorList(@RequestParam("key") String key) {
        List<Long> actorIds = RedisUtil.get(getListCacheKey(key), new TypeReference<>() {
        });
        if (Func.isEmpty(actorIds)) {
            return RspHd.data(Collections.emptyList());
        }
        Rsp<List<ActorInfoResult>> listRsp = TenantHolder.methodIgnoreTenant(() -> actorInfoClient.listByIds(actorIds));
        if (RspHd.isFail(listRsp)) {
            return RspHd.fail(listRsp);
        }
        List<ActorInfoShareResultVo> voList = Func.copy(listRsp.getData(), ActorInfoShareResultVo.class);
        voList.forEach(actorInfo -> actorInfo.setEncryptActorId(WebSecureUtil.aesEncryptParam(actorInfo.getId().toString())));
        return RspHd.data(voList);
    }

    @Operation(summary = "演员信息")
    @GetMapping("/actorInfo")
    @Auth(way = Auth.Way.ANONYMOUS)
    public Rsp<ActorInfoShareResultVo> actorInfo(@RequestParam("actorId") String encryptActorId) {
        Long actorId = WebSecureUtil.aesDecryptLongParam(encryptActorId);
        if (actorId == null) {
            return RspHd.fail("id不能为空");
        }
        Rsp<ActorInfoResult> rsp = TenantHolder.methodIgnoreTenant(() -> actorInfoClient.getById(actorId));
        if (RspHd.isFail(rsp)) {
            return RspHd.fail(rsp);
        }
        if (rsp.getData() == null) {
            return RspHd.data(null);
        }
        ActorInfoShareResultVo actorInfo = Func.copy(rsp.getData(), ActorInfoShareResultVo.class);
        return TenantHolder.methodTenant(actorInfo.getTenantId(), () -> {
            actorInfo.setEncryptActorId(WebSecureUtil.aesEncryptParam(actorInfo.getId().toString()));
            actorVideoService.setVideos(actorInfo,
                    ActorInfoShareResultVo::getId, ActorInfoShareResultVo::setVideos);
            return RspHd.data(actorInfo);
        });
    }

    private String getListCacheKey(String key) {
        return String.format("%s:content:creative:actor:share:list:%s", Func.getApplicationName(), key);
    }

}
