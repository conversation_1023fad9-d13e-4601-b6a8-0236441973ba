package com.swhd.creative.web.tenant.actor.service;

import com.swhd.creative.api.actor.client.ActorOrderCustomClient;
import com.swhd.creative.api.actor.dto.result.ActorOrderCustomResult;
import com.swhd.creative.web.tenant.actor.vo.result.ActorOrderCustomResultVo;
import com.swhd.creative.web.tenant.actor.vo.result.ActorOrderInfoResultVo;
import com.swhd.crm.api.custom.wrapper.CustomInfoWrapper;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/2/21
 */
@Service
@AllArgsConstructor
public class ActorOrderInfoService {

    private final ActorOrderCustomClient actorOrderCustomClient;

    public void setOrderCustomList(List<ActorOrderInfoResultVo> listVo) {
        if (Func.isEmpty(listVo)) {
            return;
        }
        List<Long> orderIds = listVo.stream().map(ActorOrderInfoResultVo::getId).toList();
        Rsp<List<ActorOrderCustomResult>> orderCustomListRsp = actorOrderCustomClient.listByOrderIds(orderIds);
        RspHd.failThrowException(orderCustomListRsp);
        List<ActorOrderCustomResultVo> orderCustomList = Func.copy(orderCustomListRsp.getData(),
                ActorOrderCustomResultVo.class);
        CustomInfoWrapper.getInstance().setList(orderCustomList, ActorOrderCustomResultVo::getCustomId,
                (orderCustomVo, customInfo) -> orderCustomVo.setCustomName(customInfo.getName()));
        Map<Long, List<ActorOrderCustomResultVo>> orderCustomMap = orderCustomList.stream()
                .collect(Collectors.groupingBy(ActorOrderCustomResultVo::getOrderId));
        listVo.forEach(vo -> vo.setOrderCustomList(orderCustomMap.get(vo.getId())));
    }

    public void setOrderCustomList(ActorOrderInfoResultVo vo) {
        if (vo == null) {
            return;
        }
        Rsp<List<ActorOrderCustomResult>> orderCustomListRsp = actorOrderCustomClient.listByOrderId(vo.getId());
        RspHd.failThrowException(orderCustomListRsp);
        List<ActorOrderCustomResultVo> orderCustomList = Func.copy(orderCustomListRsp.getData(),
                ActorOrderCustomResultVo.class);
        CustomInfoWrapper.getInstance().setList(orderCustomList, ActorOrderCustomResultVo::getCustomId,
                (orderCustomVo, customInfo) -> orderCustomVo.setCustomName(customInfo.getName()));
        vo.setOrderCustomList(orderCustomList);
    }

}
