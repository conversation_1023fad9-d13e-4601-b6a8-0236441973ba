package com.swhd.creative.service.material.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swhd.magiccube.mybatis.base.BaseHdEntity;
import com.swhd.magiccube.mybatis.typehandler.OssTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 视频素材信息表实体类
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "tcreative_material_oceanengine_video_info", autoResultMap = true)
public class MaterialOceanengineVideoInfo extends BaseHdEntity {

    @Schema(description = "租户id")
    private Long tenantId;

    @Schema(description = "素材ID")
    private Long materialId;

    @Schema(description = "文件名称")
    private String filename;

    @Schema(description = "视频首帧截图url")
    private String posterUrl;

    @TableField(typeHandler = OssTypeHandler.class)
    @Schema(description = "视频首帧截图oss")
    private String posterOss;

    @Schema(description = "视频时长")
    private BigDecimal videoDuration;

    @Schema(description = "视频宽度")
    private Integer videoWidth;

    @Schema(description = "视频高度")
    private Integer videoHeight;

    @Schema(description = "视频大小")
    private Long videoSize;

    @Schema(description = "最后同步数据时间")
    private LocalDateTime lastSyncTime;

}
