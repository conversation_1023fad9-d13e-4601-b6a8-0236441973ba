package com.swhd.creative.service.actor.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.creative.api.actor.dto.param.actor.order.info.ActorOrderCustomSaveParam;
import com.swhd.creative.api.actor.dto.param.actor.order.info.ActorOrderInfoAddParam;
import com.swhd.creative.api.actor.dto.param.actor.order.info.ActorOrderInfoPageParam;
import com.swhd.creative.api.actor.dto.param.actor.order.info.ActorOrderInfoUpdateParam;
import com.swhd.creative.service.actor.entity.ActorOrderCustom;
import com.swhd.creative.service.actor.entity.ActorOrderInfo;
import com.swhd.creative.service.actor.mapper.ActorOrderInfoMapper;
import com.swhd.creative.service.actor.service.ActorOrderCustomService;
import com.swhd.creative.service.actor.service.ActorOrderInfoService;
import com.swhd.creative.service.actor.service.ActorOrderMaterialService;
import com.swhd.magiccube.core.annotation.Lockable;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.mybatis.base.BaseHdServiceImpl;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.Rsp;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 创意演员订单信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-21
 */
@Service
@AllArgsConstructor
public class ActorOrderInfoServiceImpl
        extends BaseHdServiceImpl<ActorOrderInfoMapper, ActorOrderInfo>
        implements ActorOrderInfoService {

    private final ActorOrderCustomService actorOrderCustomService;

    private final ActorOrderMaterialService actorOrderMaterialService;

    @Override
    public IPage<ActorOrderInfo> page(ActorOrderInfoPageParam param) {
        return baseMapper.page(param, convertToPage(param));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp<Void> add(ActorOrderInfoAddParam param) {
        ActorOrderInfo orderInfo = Func.copy(param, ActorOrderInfo.class);
        boolean result = save(orderInfo);
        if (result) {
            saveCustomList(orderInfo.getId(), param.getCustomList());
        }
        return RspHd.status(result);
    }

    @Override
    @Lockable(prefixKey = "content:creative:actor:order:info:update", key = "#param.id", waitTime = 6000)
    public Rsp<Void> update(ActorOrderInfoUpdateParam param) {
        ActorOrderInfo oldOrderInfo = getById(param.getId());
        if (oldOrderInfo == null) {
            return RspHd.fail("订单信息不存在");
        }
        ActorOrderInfo orderInfo = Func.copy(param, ActorOrderInfo.class);
        boolean result = updateById(orderInfo);
        if (result) {
            saveCustomList(orderInfo.getId(), param.getOrderCustomList());
        }
        if (result && param.getActorId() != null && !Objects.equals(oldOrderInfo.getActorId(), param.getActorId())) {
            actorOrderMaterialService.updateActorIdByOrderId(param.getId(), param.getActorId());
        }
        return RspHd.status(result);
    }

    private void saveCustomList(Long orderId, List<ActorOrderCustomSaveParam> customList) {
        if (customList == null) {
            return;
        }
        List<ActorOrderCustom> orderCustomList = customList.stream()
                .map(saveOrderCustom -> {
                    ActorOrderCustom orderCustom = new ActorOrderCustom();
                    orderCustom.setOrderId(orderId);
                    orderCustom.setCustomId(saveOrderCustom.getCustomId());
                    orderCustom.setCaptureNumber(saveOrderCustom.getCaptureNumber());
                    return orderCustom;
                })
                .toList();
        actorOrderCustomService.saveBatch(orderId, orderCustomList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lockable(prefixKey = "content:creative:actor:order:info:remove", waitTime = 6000)
    public Rsp<Void> remove(Collection<Long> ids) {
        List<ActorOrderInfo> list = listByIds(ids);
        if (Func.isEmpty(list)) {
            return RspHd.success();
        }
        ids = list.stream().map(ActorOrderInfo::getId).toList();
        boolean result = removeByIds(list.stream().map(ActorOrderInfo::getId).toList());
        if (result) {
            actorOrderCustomService.removeByOrderIds(ids);
            actorOrderMaterialService.removeByOrderIds(ids);
        }
        return RspHd.status(result);
    }

}
