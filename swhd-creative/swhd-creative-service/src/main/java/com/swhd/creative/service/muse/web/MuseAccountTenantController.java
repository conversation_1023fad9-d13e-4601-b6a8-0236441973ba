package com.swhd.creative.service.muse.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swhd.creative.api.muse.client.MuseAccountTenantClient;
import com.swhd.creative.api.muse.dto.param.account.tenant.MuseAccountTenantAddParam;
import com.swhd.creative.api.muse.dto.param.account.tenant.MuseAccountTenantPageParam;
import com.swhd.creative.api.muse.dto.param.account.tenant.MuseAccountTenantUpdateParam;
import com.swhd.creative.api.muse.dto.result.MuseAccountTenantResult;
import com.swhd.creative.service.muse.entity.MuseAccountTenant;
import com.swhd.creative.service.muse.service.MuseAccountDistributionService;
import com.swhd.creative.service.muse.service.MuseAccountTenantService;
import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.tool.Func;
import com.swj.magiccube.api.PageResult;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.page.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-09-05
 */
@RestController
@AllArgsConstructor
@RequestMapping(MuseAccountTenantClient.BASE_PATH)
public class MuseAccountTenantController implements MuseAccountTenantClient {

    private final MuseAccountTenantService museAccountTenantService;

    private final MuseAccountDistributionService museAccountDistributionService;

    @Override
    public Rsp<PageResult<MuseAccountTenantResult>> page(MuseAccountTenantPageParam param) {
        IPage<MuseAccountTenant> iPage = museAccountTenantService.page(param);
        return RspHd.data(PageUtil.convertFromMyBatis(iPage, MuseAccountTenantResult.class));
    }

    @Override
    public Rsp<MuseAccountTenantResult> getById(Long id) {
        MuseAccountTenant entity = museAccountTenantService.getById(id);
        return RspHd.data(Func.copy(entity, MuseAccountTenantResult.class));
    }

    @Override
    public Rsp<List<MuseAccountTenantResult>> listByIds(Collection<Long> ids) {
        List<MuseAccountTenant> list = museAccountTenantService.listByIds(ids);
        return RspHd.data(Func.copy(list, MuseAccountTenantResult.class));
    }

    @Override
    public Rsp<List<MuseAccountTenantResult>> listByAccountInfoIds(Collection<Long> accountInfoIds) {
        List<MuseAccountTenant> list = museAccountTenantService.listByAccountInfoIds(accountInfoIds);
        return RspHd.data(Func.copy(list, MuseAccountTenantResult.class));
    }

    @Override
    public Rsp<List<MuseAccountTenantResult>> listAll() {
        List<MuseAccountTenant> list = museAccountTenantService.list();
        return RspHd.data(Func.copy(list, MuseAccountTenantResult.class));
    }

    @Override
    public Rsp<Void> add(MuseAccountTenantAddParam param) {
        return museAccountTenantService.add(param);
    }

    @Override
    public Rsp<Void> update(MuseAccountTenantUpdateParam param) {
        return museAccountTenantService.update(param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp<Void> removeByIds(Collection<Long> ids) {
        List<MuseAccountTenant> list = museAccountTenantService.listByIds(ids);
        if (Func.isEmpty(list)) {
            return RspHd.success();
        }
        museAccountTenantService.removeByIds(ids);
        List<Long> accountInfoIds = list.stream()
                .map(MuseAccountTenant::getAccountInfoId)
                .distinct()
                .toList();
        museAccountDistributionService.removeByAccountInfoIds(accountInfoIds);
        return RspHd.success();
    }

}
