package com.swhd.oauth.service.common.configuration;

import com.swj.magiccube.aot.annotation.AotHints;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.converters.ConverterMatcher;
import com.thoughtworks.xstream.io.naming.NameCoder;
import com.thoughtworks.xstream.persistence.PersistenceStrategy;
import me.chanjar.weixin.common.util.http.RequestHttp;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/4/3
 */
@AotHints(
        autoReflectionPackages = {
                "me.chanjar.weixin",
                "cn.binarywang.wx",
                "com.thoughtworks.xstream",
        },
        reflectionAnnotationClass = {
                XStreamAlias.class,
        },
        reflectionClass = {
                org.dom4j.DocumentFactory.class,
                org.dom4j.tree.QNameCache.class,
                org.dom4j.util.SimpleSingleton.class,
        },
        reflectionSuperClass = {
                ConverterMatcher.class,
                PersistenceStrategy.class,
                NameCoder.class,
                RequestHttp.class,
        },
        forceReflectionPackages = {
                "com.thoughtworks.xstream.converters",
                "com.thoughtworks.xstream.core",
                "com.thoughtworks.xstream.io.xml",
                "com.thoughtworks.xstream.mapper",
        }
)
@Configuration
public class OauthAotConfiguration {
}
