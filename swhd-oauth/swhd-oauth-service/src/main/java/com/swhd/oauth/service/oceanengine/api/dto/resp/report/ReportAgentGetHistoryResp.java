package com.swhd.oauth.service.oceanengine.api.dto.resp.report;

import com.google.gson.annotations.SerializedName;
import com.swhd.oauth.api.oceanengine.constant.OceanengineAccountStatus;
import com.swhd.oauth.api.oceanengine.constant.OceanengineAccountType;
import com.swhd.oauth.service.oceanengine.api.dto.resp.OceanengineCursorInfo;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/15
 */
@Getter
@Setter
public class ReportAgentGetHistoryResp {

    /**
     * 数据列表
     */
    private List<Item> list;

    /**
     * 分页信息
     */
    @SerializedName("cursor_info")
    private OceanengineCursorInfo cursorInfo;

    @Getter
    @Setter
    public static class Item {

        /**
         * 代理商ID
         */
        @SerializedName("agent_id")
        private Long agentId;

        /**
         * 代理商名称
         */
        @SerializedName("agent_name")
        private String agentName;

        /**
         * 广告主ID
         */
        @SerializedName("advertiser_id")
        private Long advertiserId;

        /**
         * 广告主名称
         */
        @SerializedName("advertiser_name")
        private String advertiserName;

        /**
         * 一级行业
         */
        @SerializedName("first_industry")
        private String firstIndustry;

        /**
         * 二级行业
         */
        @SerializedName("second_industry")
        private String secondIndustry;

        /**
         * 广告主账号的过审时间，例如2018-05-20 16:32:55
         */
        @SerializedName("audit_pass_time")
        private LocalDateTime auditPassTime;

        /**
         * 广告主账户状态：{@link OceanengineAccountStatus}
         */
        @SerializedName("account_status")
        private String accountStatus;

        /**
         * 广告主所属公司ID
         */
        @SerializedName("company_id")
        private Long companyId;

        /**
         * 广告主所属公司名称
         */
        @SerializedName("company_name")
        private String companyName;

        /**
         * 广告主账户的注册时间，例如2019-08-08 20:46:06
         */
        @SerializedName("register_time")
        private LocalDateTime registerTime;

        /**
         * 广告主账户类型：{@link OceanengineAccountType}
         */
        @SerializedName("account_source")
        private String accountSource;

        /**
         * 总消耗
         */
        private BigDecimal cost;

        /**
         * 现金消耗
         */
        @SerializedName("cash_cost")
        private BigDecimal cashCost;

        /**
         * 竞价消耗
         */
        @SerializedName("bid_cost")
        private BigDecimal bidCost;

        /**
         * 品牌消耗
         */
        @SerializedName("brand_cost")
        private BigDecimal brandCost;

        /**
         * 赠款消耗
         */
        @SerializedName("grants_cost")
        private BigDecimal grantsCost;

        /**
         * CPC消耗，即Cost Per Click广告消耗
         */
        @SerializedName("cpc_cost")
        private BigDecimal cpcCost;

        /**
         * CPM消耗，即Cost Per Mille广告消耗
         */
        @SerializedName("cpm_cost")
        private BigDecimal cpmCost;

        /**
         * oCPC消耗，即Optimized Cost per Click广告消耗
         */
        @SerializedName("ocpc_cost")
        private BigDecimal ocpcCost;

        /**
         * CPA消耗，即Cost Per Action广告消耗
         */
        @SerializedName("cpa_cost")
        private BigDecimal cpaCost;

        /**
         * oCPM消耗，即Optimized Cost Per Mille广告消耗
         */
        @SerializedName("ocpm_cost")
        private BigDecimal ocpmCost;

        /**
         * CPV消耗，即按播放计费的富媒体广告消耗
         */
        @SerializedName("cpv_cost")
        private BigDecimal cpvCost;

        /**
         * CPT消耗，即Cost Per Time广告消耗
         */
        @SerializedName("cpt_cost")
        private BigDecimal cptCost;

        /**
         * GD消耗，即Guaranteed Delivery广告消耗
         */
        @SerializedName("gd_cost")
        private BigDecimal gdCost;

        /**
         * 展示量
         */
        private Integer show;

        /**
         * 平均千次展示价格
         */
        private BigDecimal cpm;

        /**
         * 点击量
         */
        private Integer click;

        /**
         * 点击率(单位:%)
         */
        private BigDecimal ctr;

        /**
         * 平均点击价格
         */
        private BigDecimal cpc;

        /**
         * 转账总次数
         */
        @SerializedName("transfer_count")
        private Integer transferCount;

        /**
         * 今日总消耗
         */
        @SerializedName("today_cost")
        private BigDecimal todayCost;

        /**
         * 转账金额
         */
        @SerializedName("transfer_amount")
        private BigDecimal transferAmount;

        /**
         * 历史总消耗
         */
        @SerializedName("history_cost")
        private BigDecimal historyCost;

        /**
         * 广告主开户天数
         */
        @SerializedName("register_days")
        private Integer registerDays;

        /**
         * 首消时间，格式：2020-06-15 14:09:42。
         */
        @SerializedName("first_cost_time")
        private LocalDateTime firstCostTime;

    }

}
