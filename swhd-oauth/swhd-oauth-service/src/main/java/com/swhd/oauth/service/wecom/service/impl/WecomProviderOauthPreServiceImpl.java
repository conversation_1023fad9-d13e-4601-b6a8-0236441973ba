package com.swhd.oauth.service.wecom.service.impl;

import com.swhd.magiccube.core.dto.result.RspHd;
import com.swhd.magiccube.core.user.TenantHolder;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.web.log.utils.GatewayUtil;
import com.swhd.oauth.service.wecom.api.properties.WecomApiProperties;
import com.swhd.oauth.service.wecom.api.service.ApiWxCpTpService;
import com.swhd.oauth.service.wecom.service.WecomOauthAppService;
import com.swhd.oauth.service.wecom.service.WecomProviderOauthPreService;
import com.swj.magiccube.api.Constant;
import com.swj.magiccube.api.Rsp;
import com.swj.magiccube.tool.url.UrlUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.bean.WxCpTpPermanentCodeInfo;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/1/2
 */
@Slf4j
@Service
@AllArgsConstructor
public class WecomProviderOauthPreServiceImpl implements WecomProviderOauthPreService {

    private final WecomOauthAppService wecomOauthAppService;

    private final ApiWxCpTpService apiWxCpTpService;

    private final WecomApiProperties wecomApiProperties;

    @Override
    public Rsp<String> createUrl(String redirectUri) {
        String preAuthUrl;
        try {
            redirectUri = String.format("%s/swhd-oauth-web-tenant/wecomProviderOauthPre/authSuccess?redirectUri=%s",
                    GatewayUtil.getGatewayHost(), UrlUtil.urlEncode(redirectUri));
            String state = TenantHolder.getRequiredTenantId().toString();
            if (wecomApiProperties.getProvider().isTestAuth()) {
                preAuthUrl = apiWxCpTpService.getPreAuthUrl(redirectUri, state, 1);
            } else {
                preAuthUrl = apiWxCpTpService.getPreAuthUrl(redirectUri, state);
            }
            return RspHd.data(preAuthUrl);
        } catch (WxErrorException e) {
            log.error("生成预授权异常", e);
            return RspHd.fail("系统异常");
        }
    }

    @Override
    public Rsp<Void> authSuccess(String authCode, String state) {
        long tenantId;
        if (Func.isNotEmpty(state) && Func.isNumeric(state)) {
            tenantId = Long.parseLong(state);
        } else {
            // 无租户
            tenantId = -1L;
        }
        WxCpTpPermanentCodeInfo permanentCodeInfo;
        try {
            permanentCodeInfo = apiWxCpTpService.getPermanentCodeInfo(authCode);
        } catch (Exception e) {
            log.error("租户[{}]获取企业永久授权码信息异常", tenantId, e);
            return RspHd.fail("获取企业永久授权码信息异常");
        }
        WxCpTpPermanentCodeInfo.AuthCorpInfo authCorpInfo = permanentCodeInfo.getAuthCorpInfo();
        if (permanentCodeInfo.getAuthInfo() == null || Func.isEmpty(permanentCodeInfo.getAuthInfo().getAgents())) {
            log.error("租户[{}]无法获取授权应用信息", tenantId);
            return RspHd.fail("获取授权应用信息失败");
        }
        WxCpTpPermanentCodeInfo.Agent agent = permanentCodeInfo.getAuthInfo().getAgents().getFirst();
        TenantHolder.methodTenantVoid(tenantId, () -> wecomOauthAppService.providerAuthSuccess(
                authCorpInfo.getCorpName(), authCorpInfo.getCorpId(), authCorpInfo.getCorpSquareLogoUrl(),
                agent.getAgentId(), permanentCodeInfo.getPermanentCode()));
        return RspHd.success();
    }

}
