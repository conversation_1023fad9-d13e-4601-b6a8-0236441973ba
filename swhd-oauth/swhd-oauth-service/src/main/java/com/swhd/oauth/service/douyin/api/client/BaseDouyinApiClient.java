package com.swhd.oauth.service.douyin.api.client;

import com.google.gson.reflect.TypeToken;
import com.swhd.magiccube.tool.Func;
import com.swhd.magiccube.tool.GsonUtil;
import com.swhd.magiccube.tool.MaskUtil;
import com.swhd.magiccube.tool.log.JsonLogUtil;
import com.swhd.oauth.service.common.client.ApiApacheHttpClient;
import com.swhd.oauth.service.douyin.api.dto.req.BaseDouyinReq;
import com.swhd.oauth.service.douyin.api.dto.resp.BaseDouyinResp;
import com.swhd.oauth.service.douyin.api.dto.resp.DouyinRsp;
import com.swhd.oauth.service.douyin.api.dto.resp.DouyinRspExtra;
import com.swj.magiccube.api.Constant;
import com.swj.magiccube.tool.bean.BeanUtil;
import com.swj.magiccube.tool.url.UrlUtil;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/21
 */
public abstract class BaseDouyinApiClient {

    protected Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ApiApacheHttpClient apiApacheHttpClient;

    protected <Req extends BaseDouyinReq<Req>, Resp extends BaseDouyinResp> DouyinRsp<Resp> get(
            BaseDouyinReq<Req> request, TypeToken<DouyinRsp<Resp>> typeToken) {
        String reqUrl = reqUrl(request);
        try {
            CloseableHttpClient httpClient = apiApacheHttpClient.getHttpClient();
            String respBody;
            CloseableHttpResponse response;
            HttpGet get = new HttpGet(reqUrl);
            request.getHeaders().forEach(get::addHeader);
            response = httpClient.execute(get);
            HttpEntity httpEntity = response.getEntity();
            respBody = EntityUtils.toString(httpEntity, StandardCharsets.UTF_8);
            EntityUtils.consume(httpEntity);
            return resp(get, request, respBody, typeToken);
        } catch (Exception e) {
            log.error("【抖音】请求接口异常, reqUrl:[{}], headers:[{}], reqBody:[{}]",
                    reqUrl, maskHeaders(request.getHeaders()), JsonLogUtil.toJsonString(request), e);
            return exceptionDouyinRsp(e, typeToken);
        }
    }

    protected <Req extends BaseDouyinReq<Req>, Resp extends BaseDouyinResp> DouyinRsp<Resp> postJson(
            BaseDouyinReq<Req> request, TypeToken<DouyinRsp<Resp>> typeToken) {
        String reqUrl = reqUrl(request);
        try {
            CloseableHttpClient httpClient = apiApacheHttpClient.getHttpClient();
            String respBody;
            CloseableHttpResponse response;
            HttpPost post = new HttpPost(reqUrl);
            request.getHeaders().forEach(post::addHeader);
            StringEntity entity = new StringEntity(GsonUtil.toJsonString(request), ContentType.APPLICATION_JSON);
            // 携带; charset=UTF-8导致接口返回参数错误
            entity.setContentType("application/json");
            post.setEntity(entity);
            response = httpClient.execute(post);
            HttpEntity httpEntity = response.getEntity();
            respBody = EntityUtils.toString(httpEntity, StandardCharsets.UTF_8);
            EntityUtils.consume(httpEntity);
            return resp(post, request, respBody, typeToken);
        } catch (Exception e) {
            log.error("【抖音】请求接口异常, reqUrl:[{}], headers:[{}], reqBody:[{}]",
                    reqUrl, maskHeaders(request.getHeaders()), JsonLogUtil.toJsonString(request), e);
            return exceptionDouyinRsp(e, typeToken);
        }
    }

    protected <Req extends BaseDouyinReq<Req>, Resp extends BaseDouyinResp> DouyinRsp<Resp> postForm(
            BaseDouyinReq<Req> request, TypeToken<DouyinRsp<Resp>> typeToken) {
        String reqUrl = reqUrl(request);
        try {
            CloseableHttpClient httpClient = apiApacheHttpClient.getHttpClient();
            String respBody;
            CloseableHttpResponse response;
            HttpPost post = new HttpPost(reqUrl);
            request.getHeaders().forEach(post::addHeader);
            MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
            request.getFormData().entrySet().stream()
                    .filter(entry -> entry.getValue() != null)
                    .forEach(entry -> {
                        if (entry.getValue() instanceof File file) {
                            multipartEntityBuilder.addBinaryBody(entry.getKey(), file);
                        } else {
                            multipartEntityBuilder.addTextBody(entry.getKey(), entry.getValue().toString());
                        }
                    });
            post.setEntity(multipartEntityBuilder.build());
            response = httpClient.execute(post);
            HttpEntity httpEntity = response.getEntity();
            respBody = EntityUtils.toString(httpEntity, StandardCharsets.UTF_8);
            EntityUtils.consume(httpEntity);
            return resp(post, request, respBody, typeToken);
        } catch (Exception e) {
            log.error("【抖音】请求接口异常, reqUrl:[{}], headers:[{}], reqBody:[{}]",
                    reqUrl, maskHeaders(request.getHeaders()), JsonLogUtil.toJsonString(request), e);
            return exceptionDouyinRsp(e, typeToken);
        }
    }

    private <Req extends BaseDouyinReq<Req>, Resp extends BaseDouyinResp> DouyinRsp<Resp> resp(
            HttpRequestBase httpRequest, BaseDouyinReq<Req> request, String respBody, TypeToken<DouyinRsp<Resp>> typeToken) {
        log.info("【抖音】请求接口成功, reqUrl:[{}], headers:[{}], reqBody:[{}]",
                httpRequest.getURI(), maskHeaders(request.getHeaders()), JsonLogUtil.toJsonString(request));
        log.debug("【抖音】接口返回数据：{}", respBody);
        if (Func.isBlank(respBody)) {
            log.error("【抖音】接口返回数据为空");
            return nullDouyinRsp(typeToken);
        }
        DouyinRsp<Resp> rsp = GsonUtil.parseObject(respBody, typeToken);
        if (rsp == null) {
            rsp = nullDouyinRsp(typeToken);
        }
        if (!rsp.isSuccess()) {
            log.warn("【抖音】接口返回失败：{}", respBody);
        }

        // 反序列化额外字段
        Type mapType = new TypeToken<Map<String, Object>>() {
        }.getType();
        Map<String, Object> map = GsonUtil.parseObject(respBody, mapType);
        Map<String, Object> additionalProperty = map.entrySet().stream()
                .filter(entry -> !entry.getKey().equals("extra"))
                .filter(entry -> !entry.getKey().equals("data"))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        rsp.setAdditionalProperty(additionalProperty);

        return rsp;
    }

    private static <T extends BaseDouyinResp> DouyinRsp<T> nullDouyinRsp(TypeToken<DouyinRsp<T>> typeToken) {
        return exceptionDouyinRsp("网络异常", typeToken);
    }

    private static <T extends BaseDouyinResp> DouyinRsp<T> exceptionDouyinRsp(Exception e, TypeToken<DouyinRsp<T>> typeToken) {
        String errorMessage = String.format("【抖音】请求接口异常, message:[%s]", e.getMessage());
        return exceptionDouyinRsp(errorMessage, typeToken);
    }

    private static <T extends BaseDouyinResp> DouyinRsp<T> exceptionDouyinRsp(String errorMessage, TypeToken<DouyinRsp<T>> typeToken) {
        DouyinRsp<T> rsp = new DouyinRsp<>();
        DouyinRspExtra extra = new DouyinRspExtra();
        extra.setErrorCode(-1);
        extra.setDescription(errorMessage);
        rsp.setExtra(extra);
        T resp = BeanUtil.newInstance(getDataType(typeToken));
        resp.setErrorCode(-1);
        resp.setDescription(errorMessage);
        rsp.setData(resp);
        return rsp;
    }

    /**
     * 生成reqUrl
     */
    private static String reqUrl(BaseDouyinReq<?> request) {
        if (request.getParams().isEmpty()) {
            return request.reqUrl();
        }
        String queryString = request.getParams().entrySet().stream()
                .map(entry -> String.format("%s=%s", entry.getKey(), UrlUtil.urlEncode(entry.getValue())))
                .collect(Collectors.joining("&"));
        if (Func.isEmpty(queryString)) {
            return request.reqUrl();
        }
        return request.reqUrl() + Constant.Str.QUESTION + queryString;
    }

    private static String maskHeaders(Map<String, String> headers) {
        Map<String, String> maskHeaders = new HashMap<>(headers);
        String accessToken = maskHeaders.get(BaseDouyinReq.ACCESS_TOKEN_HEADER_NAME);
        if (Func.isNotEmpty(accessToken)) {
            maskHeaders.put(BaseDouyinReq.ACCESS_TOKEN_HEADER_NAME, MaskUtil.passwordMask());
        }
        return JsonLogUtil.toJsonString(maskHeaders);
    }

    private static <T extends BaseDouyinResp> Class<T> getDataType(TypeToken<DouyinRsp<T>> typeToken) {
        Type superclass = typeToken.getClass().getGenericSuperclass();
        ParameterizedType parameterized = (ParameterizedType) superclass;
        // DouyinRsp
        parameterized = (ParameterizedType) parameterized.getActualTypeArguments()[0];
        // DouyinRsp<T>
        return (Class<T>) parameterized.getActualTypeArguments()[0];
    }

}
