package com.swhd.oauth.service.oceanengine.api.dto.req.agent;

import com.google.gson.annotations.SerializedName;
import com.swhd.oauth.service.oceanengine.api.constant.RequestMethod;
import com.swhd.oauth.service.oceanengine.api.dto.req.BaseOceanenginePageReq;
import com.swhd.oauth.service.oceanengine.api.dto.resp.agent.AgentTransactionRecordResp;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/13
 */
@Getter
@Setter
@Accessors(chain = true)
public class AgentTransactionRecordReq extends BaseOceanenginePageReq<AgentTransactionRecordResp> {

    /**
     * 代理商id
     */
    @SerializedName("agent_id")
    private Long agentId;

    /**
     * 开始时间，格式 yyyy-MM-dd，最远可以查询3年内的数据
     */
    @SerializedName("start_date")
    private LocalDate startDate;

    /**
     * 截止时间，格式 yyyy-MM-dd
     */
    @SerializedName("end_date")
    private LocalDate endDate;

    /**
     * 过滤器
     */
    private Filtering filtering;

    @Override
    public RequestMethod getMethod() {
        return RequestMethod.GET_QUERY;
    }

    @Override
    public String getUrl() {
        return "https://api.oceanengine.com/open_api/2/agent/transfer/transaction_record/";
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Filtering {

        /**
         * 转入/转出方客户
         */
        @SerializedName("customer_ids")
        private List<Long> customerIds;

        /**
         * 转入/转出方账户
         */
        @SerializedName("account_ids")
        private List<Long> accountIds;

        /**
         * 转入方账号
         */
        @SerializedName("payees")
        private List<Long> payees;

        /**
         * 转入方客户
         */
        @SerializedName("payee_customer_ids")
        private List<Long> payeeCustomerIds;

        /**
         * 转出方账户
         */
        @SerializedName("remitters")
        private List<Long> remitters;

        /**
         * 转出方客户
         */
        @SerializedName("remitter_customer_ids")
        private List<Long> remitterCustomerIds;

        /**
         * 操作人
         */
        @SerializedName("operator_ids")
        private List<Long> operatorIds;

        /**
         * 转账编号
         */
        @SerializedName("transfer_order_serial")
        private String transferOrderSerial;

        /**
         * 转账类型 可选值:
         * ADD_MONEY 加款
         * MUTUAL_TRANSFER 同级账户转账
         * REFUND_MONEY 退款
         */
        @SerializedName("transfer_type")
        private String transferType;

        /**
         * 转出方账户类型 可选值:
         * ROLE_ADVERTISER 广告主
         * ROLE_AGENT 广告代理商
         * ROLE_CHILD_AGENT 代理子账户
         * ROLE_ECP_VIRTUAL_ADVERTISER 千川虚拟广告主
         * ROLE_LOCAL_LIFE_VIRTUAL_ADVERTISER 本地虚拟广告主
         * ROLE_VIRTAUL_ADVERTISER 虚拟广告主
         */
        @SerializedName("remitter_type")
        private String remitterType;

        /**
         * 转入方账户类型 可选值:
         * ROLE_ADVERTISER 广告主
         * ROLE_AGENT 广告代理商
         * ROLE_CHILD_AGENT 代理子账户
         * ROLE_ECP_VIRTUAL_ADVERTISER 千川虚拟广告主
         * ROLE_LOCAL_LIFE_VIRTUAL_ADVERTISER 本地虚拟广告主
         * ROLE_VIRTAUL_ADVERTISER 虚拟广告主
         */
        @SerializedName("payee_type")
        private String payeeType;

        /**
         * 业务平台 可选值:
         * AD 巨量广告
         * EFFECT 效果联盟
         * STAR 巨量星图
         * TRUSTEESHIP_MARKETING 托管营销
         * ZTT 智投通
         */
        @SerializedName("platform")
        private String platform;

        /**
         * 转出方代理商账户
         */
        @SerializedName("remitter_first_ad_agent_id")
        private Long remitterFirstAdAgentId;

        /**
         * 转入方代理商账户
         */
        @SerializedName("payee_first_ad_agent_id")
        private String payeeFirstAdAgentId;

    }

}
