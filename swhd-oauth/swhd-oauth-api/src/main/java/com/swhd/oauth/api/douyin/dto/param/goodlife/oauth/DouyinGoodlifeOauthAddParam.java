package com.swhd.oauth.api.douyin.dto.param.goodlife.oauth;

import com.swhd.oauth.api.douyin.constant.GoodlifeOauthScope;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-19
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "DouyinGoodlifeOauthAddParam对象")
public class DouyinGoodlifeOauthAddParam {

    @NotEmpty(message = "商家ID不能为空")
    @Schema(description = "商家ID")
    private String bizId;

    @Schema(description = "商家名称")
    private String bizName;

    @Schema(description = "授权功能")
    private List<GoodlifeOauthScope> scopes;

}
