package com.swhd.oauth.api.wechat.dto.param.api.mp.kefubuilder;

import com.swhd.oauth.api.wechat.constant.WechatMpConstant;
import com.swhd.oauth.api.wechat.dto.param.api.mp.WechatApiMpKefuMessageParam;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 菜单消息builder
 * <pre>
 * 用法:
 * WxMpKefuMessage m = WxMpKefuMessage.MSGMENU().addMenus(lists).headContent(headContent).tailContent(tailContent).toUser(...).build();
 * </pre>
 *
 * <AUTHOR>
 * @since 2021/8/31
 */
@Schema
public class WxMsgMenuBuilder extends BaseBuilder<WxMsgMenuBuilder> {

    private List<WechatApiMpKefuMessageParam.MsgMenu> msgMenus = new ArrayList<>();

    private String headContent;

    private String tailContent;

    public WxMsgMenuBuilder() {
        this.msgType = WechatMpConstant.KefuMsgType.MSGMENU;
    }

    public WxMsgMenuBuilder addMenus(WechatApiMpKefuMessageParam.MsgMenu... msgMenus) {
        Collections.addAll(this.msgMenus, msgMenus);
        return this;
    }

    public WxMsgMenuBuilder msgMenus(List<WechatApiMpKefuMessageParam.MsgMenu> msgMenus) {
        this.msgMenus = msgMenus;
        return this;
    }

    public WxMsgMenuBuilder headContent(String headContent) {
        this.headContent = headContent;
        return this;
    }

    public WxMsgMenuBuilder tailContent(String tailContent) {
        this.tailContent = tailContent;
        return this;
    }

    @Override
    public WechatApiMpKefuMessageParam build() {
        WechatApiMpKefuMessageParam m = super.build();
        m.setHeadContent(this.headContent);
        m.setTailContent(this.tailContent);
        m.setMsgMenus(this.msgMenus);
        return m;
    }

}
