package com.swhd.oauth.api.oceanengine.dto.result;

import com.google.gson.annotations.SerializedName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/13
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "OceanengineRiskControlGetPromotionListResult对象")
public class OceanengineRiskControlSpiTaskListResult {
    @SerializedName("list")
    private List<SpiTaskList> list;


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class SpiTaskList {

        /**
         * 广告ID
         */
        @SerializedName("task_id")
        private Long taskId;
        /**
         * 广告名称
         */
        @SerializedName("data")
        private String data;

        /**
         * 项目ID
         */
        @SerializedName("status")
        private String status;
    }
}
