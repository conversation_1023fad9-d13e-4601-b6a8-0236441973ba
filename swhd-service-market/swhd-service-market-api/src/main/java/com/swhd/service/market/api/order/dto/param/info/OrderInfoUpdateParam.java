package com.swhd.service.market.api.order.dto.param.info;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.swhd.service.market.api.product.dto.result.ProductSkuDetailResult;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024-11-12
 */
@Getter
@Setter
@Accessors(chain = true)
@Schema(description = "OrderInfoUpdateParam对象")
public class OrderInfoUpdateParam {

    @Schema(description = "主键id")
    @NotNull(message = "主键id不能为空")
    private Long id;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "订单名称")
    private String orderName;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "下单渠道编码")
    private String channelCode;

    @Schema(description = "订单类型：0-付费订单；1-赠送订单；2-试用")
    private Integer orderType;

    @Schema(description = "订单状态：0-待支付;1-待处理;2-处理完成;3-处理失败; 4-取消;5-超时关闭;6-已退款;")
    private Integer orderStatus;

    @Schema(description = "sku编码")
    private String skuCode;

    @Schema(description = "sku详情快照")
    private ProductSkuDetailResult skuInfo;

    @Schema(description = "下单时间")
    private LocalDateTime orderTime;

    @Schema(description = "总价")
    private BigDecimal price;

    @Schema(description = "数量")
    private Integer num;

    @Schema(description = "激活时间")
    private LocalDateTime activateTime;

    @Schema(description = "防重ID")
    private String nonce;

    @Schema(description = "订单备注")
    private String orderRemarks;

    @Schema(description = "处理说明")
    private String processRemark;

}
