package com.swhd.service.market.web.tenant.product.config;

import com.swhd.service.market.api.product.dto.param.sku.Benefit;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "membership")
public class MembershipConfig {


    private MemberPersonalConfig memberPersonal = new MemberPersonalConfig();


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MemberPersonalConfig {
        private List<MemberPersonalSpecialSku> specialSkuList = new ArrayList<>();

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MemberPersonalSpecialSku {

        @Schema(description = "会员类型 non_member:非会员 personal:个人版 enterprise:企业版")
        private String memberType;

        @Schema(description = "标题")
        private String title;

        @Schema(description = "原价")
        private BigDecimal originalPrice;

        @Schema(description = "现价")
        private String priceText;

        @Schema(description = "售价单位，如：元/年、元/月、元/季、元/半年")
        private String priceUnitText;

        @Schema(description = "权益描述列表")
        private List<Benefit> benefitList = new ArrayList<>();

        @Schema(description = "排序")
        private int ordered = 0;

    }

} 